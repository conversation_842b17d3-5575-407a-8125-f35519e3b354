.agreement-container {
  padding: 20rpx;
  background-color: #f8f8f8;
  min-height: 100vh;
}

.agreement-header {
  text-align: center;
  padding: 30rpx 0;
  background-color: #fff;
  margin-bottom: 20rpx;
  border-radius: 10rpx;
}

.agreement-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.agreement-date {
  display: block;
  font-size: 24rpx;
  color: #999;
}

.agreement-content {
  background-color: #fff;
  border-radius: 10rpx;
  padding: 30rpx;
  max-height: calc(100vh - 200rpx);
}

.agreement-section {
  margin-bottom: 40rpx;
}

.section-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  line-height: 1.5;
}

.section-content {
  display: block;
  font-size: 28rpx;
  color: #666;
  line-height: 1.8;
  margin-bottom: 15rpx;
  text-indent: 2em;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6rpx;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10rpx;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 10rpx;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
